[2m2025-07-28 00:04:40.150[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.220[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.242[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.243[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:40.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:40.280[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:04:42.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.208[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.210[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.211[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.215[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:42.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:04:44.843[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.845[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.986[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.990[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.993[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:45.062[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String)
[2m2025-07-28 00:04:45.129[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 9
[2m2025-07-28 00:04:51.978[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.985[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==>  Preparing: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String), 打印设计测试(String), /bank-note/label-hiprint/label-designer-v2(String), /bank-note/label-hiprint/label-designer-v2(String), 1(Integer), 60(Integer), 0(Integer), e8c7c1c9120f8b26d9a841d6379624d0(String)
[2m2025-07-28 00:04:52.009[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:04:52.146[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:52.186[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:52.207[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:05:02.523[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.534[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.536[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.537[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.541[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-07-28 00:05:02.563[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:05:02.568[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-28 00:05:02.576[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-28 00:05:02.578[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-28 00:05:02.579[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-28 00:05:02.579[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-07-28 00:05:02.585[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 00:05:02.588[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-07-28 00:05:02.595[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-07-28 00:05:02.596[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-07-28 00:05:02.597[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-07-28 00:05:02.597[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-07-28 00:05:02.625[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-07-28 00:05:02.964[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:02.976[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:02.979[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:03.009[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:03.010[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:05:03.015[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:05:03.025[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:05:03.026[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:05:03.035[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:05:48.306[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.335[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.344[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.350[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:05:48.364[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:05:57.402[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.415[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.417[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.418[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.420[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:05:57.428[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:07:34.601[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.638[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.639[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.648[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.649[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:07:34.655[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:07:47.442[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.458[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.467[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.469[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.471[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:07:47.480[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:08:43.607[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.656[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.685[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.686[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.687[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:08:43.695[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:08:53.710[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.745[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.757[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.758[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.760[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:08:53.778[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:09:17.593[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.639[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.640[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.640[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.641[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:09:17.657[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:09:29.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.375[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.376[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.380[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.381[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:09:29.389[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:10:13.493[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.504[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.505[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.505[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.508[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:10:13.515[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:06.914[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.948[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.958[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.964[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:06.980[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:19.314[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.330[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.333[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.335[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.336[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:19.405[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:30.323[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.340[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.340[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.341[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.342[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:30.351[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:41.217[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.252[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.281[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.303[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:41.327[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:56.494[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.535[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.543[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.549[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.550[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:56.559[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:13:22.321[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-07-28 00:13:25.561[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=6082917, minRoundTripTimeNanos=0}
[2m2025-07-28 00:13:32.501[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.591[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.599[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.623[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.624[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:13:32.629[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:14:59.241[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:14:59.265[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:14:59.273[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:14:59.289[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:14:59.290[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:14:59.308[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:14:59.347[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.362[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.362[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.366[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.367[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:14:59.378[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:15:07.476[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:15:07.491[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:15:07.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:15:07.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:15:07.498[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:15:07.514[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:15:07.550[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.564[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:15:07.570[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:15:08.083[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.098[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.102[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:15:08.112[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:15.829[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:16:15.839[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:16:15.842[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:16:15.848[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:16:15.849[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:16:15.861[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:16:15.887[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.896[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:16:15.904[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:18.803[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.454[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:16:19.463[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:59.933[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:16:59.943[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:16:59.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:16:59.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:16:59.947[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":50,"width":191,"paperHeader":0,"paperFooter":141.73228346456693,"printElements":[],"paperNumberLeft":511,"paperNumberTop":119,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T00:16:59.929813(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 00:16:59.966[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:16:59.995[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:00.003[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:00.005[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:00.006[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:00.006[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:17:00.033[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:17:28.865[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:17:28.872[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":50,"width":191,"paperHeader":0,"paperFooter":141.73228346456693,"printElements":[{"options":{"left":0,"top":0,"height":136.5,"width":115.5,"right":138,"bottom":136.5,"vCenter":69,"hCenter":68.25},"printElementType":{"title":"矩形","type":"rect"}}],"paperNumberLeft":511,"paperNumberTop":119,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T00:17:28.864207(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 00:17:28.887[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:17:28.916[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:28.923[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:28.924[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:28.926[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:28.927[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:17:28.937[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:39:16.895[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:39:16.934[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:39:16.939[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:39:16.950[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:39:16.953[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:39:16.967[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:39:17.005[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.018[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.018[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.022[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.022[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:39:17.033[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:40:44.140[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:40:44.197[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:40:44.199[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:40:44.210[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:40:44.211[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:40:44.222[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:40:44.249[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.255[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:40:44.262[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:40:46.174[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.183[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.184[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.184[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:40:46.190[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:46:12.969[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:46:12.999[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:46:13.002[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:46:13.010[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:46:13.012[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:46:13.026[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:46:13.055[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.062[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.064[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.064[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.065[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:46:13.070[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:46:14.562[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.570[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.571[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.572[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.572[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:46:14.577[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:55:43.403[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:55:43.417[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:55:43.420[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:55:43.488[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:55:43.489[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:55:43.625[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:55:43.660[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.667[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:55:43.675[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:55:47.116[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.134[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.136[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.140[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.141[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:55:47.152[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:07:20.694[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:07:20.718[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:07:20.720[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:07:20.730[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:07:20.730[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:07:20.752[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:07:20.781[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.788[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.790[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.791[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.791[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:07:20.797[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:07:22.083[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.090[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:07:22.101[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:09:36.545[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:09:36.558[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:09:36.560[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:09:36.570[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:09:36.571[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:09:36.579[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:13:15.610[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:13:15.625[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:13:15.628[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:13:15.635[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:13:15.636[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:13:15.647[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:13:15.675[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:15.683[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:15.685[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:15.685[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:15.686[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:13:15.691[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:13:16.695[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:16.707[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:16.710[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:16.711[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:16.712[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:13:16.717[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:13:25.506[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:13:25.549[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:13:25.552[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:13:25.553[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:13:25.556[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:13:25.575[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:13:25.601[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:25.607[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:25.609[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:25.609[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:25.610[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:13:25.615[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:13:26.298[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:26.305[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:26.308[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:26.308[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:13:26.309[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:13:26.318[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:15:01.600[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:15:01.618[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:15:01.626[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:15:01.654[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:15:01.655[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:15:01.672[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:15:01.698[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:01.704[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:01.706[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:01.706[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:01.706[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:15:01.712[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:15:03.230[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:03.237[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:03.240[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:03.240[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:15:03.240[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:15:03.246[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:18:10.071[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:18:10.090[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:18:10.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:18:10.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:18:10.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:18:10.120[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:18:10.151[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.157[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.159[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.160[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.160[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:18:10.166[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:18:10.931[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.938[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.940[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.940[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:18:10.940[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:18:10.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:34:05.317[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:34:05.358[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:34:05.363[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:34:05.371[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:34:05.372[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:34:05.387[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:34:05.429[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:34:05.436[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:34:05.439[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:34:05.440[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:34:05.440[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:34:05.446[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:37:10.886[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:37:10.931[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:37:10.935[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:37:10.955[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:37:10.956[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:37:10.972[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:37:11.015[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:11.034[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:11.034[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:11.034[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:11.034[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:37:11.045[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:37:12.901[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:12.972[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:12.978[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:12.979[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:37:12.981[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:37:12.998[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:40:41.556[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:40:41.591[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:40:41.593[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:40:41.600[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:40:41.600[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:40:41.615[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:40:41.644[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:40:41.651[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:40:41.652[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:40:41.652[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:40:41.653[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:40:41.658[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:41:17.678[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:41:17.691[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:41:17.694[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:41:17.695[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:41:17.696[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:41:17.709[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:43:26.905[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:43:26.922[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:43:26.927[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:43:26.938[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:43:26.938[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:43:26.959[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:43:27.011[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:27.018[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:27.020[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:27.021[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:27.022[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:43:27.027[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:43:33.272[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:33.288[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:33.290[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:33.296[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:43:33.299[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:43:33.307[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:46:40.349[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-07-28 01:46:43.569[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=9329708, minRoundTripTimeNanos=0}
[2m2025-07-28 01:47:27.521[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:47:27.550[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:47:27.553[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:47:27.580[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m entering args (oracle.jdbc.internal.AbstractConnectionBuilder$1@16efb2cc)
[2m2025-07-28 01:47:27.581[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=49F6DF3B. 
[2m2025-07-28 01:47:27.584[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Session Attributes: 
sdu=8192, tdu=2097152
nt: host=paynexc.home, port=1521, socketOptions={0=YES, 1=NO, 17=0, 18=false, 2=10000, 20=true, 38=TLS, 23=40, 24=50, 40=false, 25=0}
    socket=null
client profile={oracle.net.encryption_types_client=(), oracle.net.crypto_seed=, oracle.net.authentication_services=(BEQ), oracle.net.setFIPSMode=false, oracle.net.kerberos5_mutual_authentication=false, oracle.net.encryption_client=ACCEPTED, oracle.net.crypto_checksum_client=ACCEPTED, oracle.net.crypto_checksum_types_client=()}
connection options=[host=paynexc.home port=1521 sid=ORCL protocol=TCP addr=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521)) conn_data=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521))(CONNECT_DATA=(CID=(PROGRAM=JDBC Thin Client)(HOST=__jdbc__)(USER=paynexc))(SID=ORCL))) done=true]
onBreakReset=false, dataEOF=false, negotiatedOptions=0x841, connected=false
[2m2025-07-28 01:47:27.584[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=49F6DF3B, anoEnabled=true. 
[2m2025-07-28 01:47:27.584[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Got Resend, SessionTraceId = 49F6DF3B
[2m2025-07-28 01:47:27.584[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Connection established. Cleared inet addresses in conn option and conn strategy stack
[2m2025-07-28 01:47:27.584[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m returning void
[2m2025-07-28 01:47:27.585[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-07-28 01:47:27.593[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m 

java.sql.SQLRecoverableException: ORA-17002: IO 错误: Connection reset
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:978)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1205)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1135)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1472)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1343)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1727)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:380)
	at com.alibaba.druid.pool.ValidConnectionCheckerAdapter.execValidQuery(ValidConnectionCheckerAdapter.java:68)
	at com.alibaba.druid.pool.vendor.OracleValidConnectionChecker.isValidConnection(OracleValidConnectionChecker.java:67)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1530)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1553)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1484)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at jdk.internal.reflect.GeneratedMethodAccessor230.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.payne.core.handler.DecryptInterceptor.intercept(DecryptInterceptor.java:33)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy246.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy246.query(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy246.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor221.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy152.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy205.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:406)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl.getTemplateList(LabelDesignServiceImpl.java:107)
	at jdk.internal.reflect.GeneratedMethodAccessor477.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl$$SpringCGLIB$$0.getTemplateList(<generated>)
	at com.payne.server.banknote.controller.LabelDesignController.getTemplateList(LabelDesignController.java:83)
	at jdk.internal.reflect.GeneratedMethodAccessor476.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at oracle.net.nt.TimeoutSocketChannel.readFromSocket(TimeoutSocketChannel.java:743)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:496)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1222)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:271)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:204)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:147)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:120)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:104)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:875)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:446)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:888)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:963)
	... 202 common frames omitted

[2m2025-07-28 01:47:27.597[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m properties={socksProxyHost=127.0.0.1, java.specification.version=17, kotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true, sun.arch.data.model=64, java.vendor.url=http://www.azul.com/, APPLICATION_NAME=payneServer, sun.boot.library.path=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/lib, sun.java.command=com.payne.App, jdk.debug=release, spring.liveBeansView.mbeanDomain=, java.specification.vendor=Oracle Corporation, java.version.date=2025-04-15, java.home=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home, rebel.env.ide.version=2025.1.4.1, java.vm.specification.vendor=Oracle Corporation, java.specification.name=Java Platform API Specification, user.script=Hans, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, java.runtime.version=17.0.15+6-LTS, file.encoding=UTF-8, java.vendor.version=Zulu17.58+21-CA, java.io.tmpdir=/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/, java.version=17.0.15, java.vm.specification.name=Java Virtual Machine Specification, CONSOLE_LOG_CHARSET=UTF-8, native.encoding=UTF-8, java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:., LOCALE=zh_CN_#Hans, java.vendor=Azul Systems, Inc., DriverVersion=********.0, java.specification.maintenance.version=1, sun.io.unicode.encoding=UnicodeBig, LOGGED_APPLICATION_NAME=[payneServer] , DatabaseProductVersion=19000, https.proxyPort=6152, user.timezone=Asia/Shanghai, org.jboss.logging.provider=slf4j, java.vm.specification.version=17, os.name=Mac OS X, spring.application.admin.enabled=true, com.sun.management.jmxremote=, http.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, user.home=/Users/<USER>/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.17168474428531153151, os.arch=aarch64, catalina.base=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.17168474428531153151, java.vm.info=mixed mode, emulated-client, java.class.version=61.0, http.proxyPort=6152, sun.jnu.encoding=UTF-8, rebel.env.ide.plugin.build=b24d9c632dac6f2feb2d26fcc8959296173ecc63, catalina.useNaming=false, file.separator=/, java.vm.compressedOopsMode=Zero based, line.separator=
, intellij.debug.agent=true, rebel.notification.url=http://localhost:17434, user.name=paynexc, URL=****************************************, management.endpoints.jmx.exposure.include=*, jboss.modules.system.pkgs=com.intellij.rt, socksProxyPort=6153, PID=69539, kotlinx.coroutines.debug.enable.flows.stack.trace=true, java.rmi.server.randomIDs=true, debugger.agent.enable.coroutines=true, socksNonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, http.proxyHost=127.0.0.1, java.class.path=/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar, java.vm.vendor=Azul Systems, Inc., sun.java.launcher=SUN_STANDARD, user.country=CN, sun.cpu.endian=little, user.language=zh, https.proxyHost=127.0.0.1, ftp.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, spring.jmx.enabled=true, rebel.env.ide.plugin.version=2025.3.0, java.runtime.name=OpenJDK Runtime Environment, rebel.native.image=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/jrebel-JRebel-202506301043/lib/libjrebel64.dylib, rebel.env.ide=intellij, rebel.plugins=/Users/<USER>/Library/Application Support/JetBrains/IntelliJIdea2025.1/plugins/jr-mp-ide-idea/lib/jr-mybatisplus-1.0.7.jar, java.vendor.url.bug=http://www.azul.com/support/, user.dir=/Users/<USER>/project/ele-admin, java.vm.version=17.0.15+6-LTS, rebel.base=/Users/<USER>/.jrebel, rebel.env.ide.product=IU}. 
[2m2025-07-28 01:47:27.728[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:47:27.730[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:47:27.747[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:47:27.771[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:27.778[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:27.780[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:27.780[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:27.780[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:47:27.786[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:47:34.906[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:34.918[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:34.921[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:34.921[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:34.922[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:47:34.929[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:47:59.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:47:59.290[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:47:59.293[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:47:59.294[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:47:59.295[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:47:59.311[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:47:59.337[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:59.344[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:59.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:59.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:47:59.347[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:47:59.354[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:48:01.849[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:48:01.855[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:48:01.857[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:48:01.857[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:48:01.858[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:48:01.863[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:50:10.811[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:50:10.833[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:50:10.836[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:50:10.852[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:50:10.853[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:50:10.866[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:50:10.893[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:10.900[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:10.902[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:10.903[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:10.903[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:50:10.908[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:50:11.832[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:11.840[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:11.841[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:11.843[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:11.844[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:50:11.849[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:50:37.636[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:50:37.829[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:50:37.831[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:50:37.832[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:50:37.833[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:50:37.848[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:50:37.891[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:37.897[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:37.899[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:37.899[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:37.900[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:50:37.905[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:50:44.102[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:44.115[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:44.117[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:44.118[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:50:44.118[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:50:44.124[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:52:13.601[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:52:13.616[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:52:13.619[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:52:13.619[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:52:13.620[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[],"paperNumberLeft":389,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":2,"name":3,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T01:52:13.586109(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 01:52:13.639[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 01:52:13.675[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:52:13.693[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:52:13.696[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:52:13.696[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:52:13.696[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:52:13.708[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:52:14.916[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:52:14.936[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:52:14.939[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:52:14.940[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:52:14.940[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:52:14.954[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:52:14.979[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:14.986[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:14.986[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:14.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:14.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:52:14.993[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:52:15.646[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:15.652[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:15.654[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:15.654[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:52:15.654[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:52:15.660[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:24.751[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:24.762[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:24.762[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:24.770[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:24.770[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:24.776[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:25.491[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.498[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.501[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.501[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.501[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:25.507[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:25.972[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.985[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:25.990[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:25.996[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:26.125[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:26.132[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:26.132[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:26.135[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:26.136[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:26.143[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:26.950[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:53:27.026[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:53:27.027[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:53:27.028[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:53:27.028[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:53:27.043[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:53:27.076[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:27.082[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:27.084[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:27.084[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:27.085[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:27.089[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:53:28.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:28.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:28.259[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:28.260[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:53:28.260[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:53:28.266[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:56:05.402[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:56:05.407[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:56:05.411[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:56:05.411[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:56:05.411[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":25.5,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"恰比编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":389,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":2,"name":3,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":3,"name":4,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T01:56:05.397119(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 01:56:05.436[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 01:56:05.484[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:56:05.494[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:56:05.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:56:05.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:56:05.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:56:05.508[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:59:51.474[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:59:51.485[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 01:59:51.487[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:59:51.488[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 01:59:51.489[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":25.5,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"恰比编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"68","right":438,"bottom":39.75,"vCenter":403.5,"hCenter":20.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"Superb Gem Unc","right":439.74609375,"bottom":61.74609375,"vCenter":405.24609375,"hCenter":55.74609375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":2,"name":3,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":3,"name":4,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T01:59:51.465496(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 01:59:51.515[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 01:59:51.545[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:59:51.554[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:59:51.559[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:59:51.560[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:59:51.560[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:59:51.575[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 02:00:06.324[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:00:06.338[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:00:06.341[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:00:06.341[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:00:06.341[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 02:00:06.353[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 02:00:06.378[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:06.385[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:06.387[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:06.387[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:06.387[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 02:00:06.393[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 02:00:07.262[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:07.273[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:07.288[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:07.288[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:00:07.289[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 02:00:07.295[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 02:00:48.532[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 02:00:48.540[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 02:00:48.542[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 02:00:48.542[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 02:00:48.543[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"68","right":438,"bottom":39.75,"vCenter":403.5,"hCenter":20.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":25.5,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"Superb Gem Unc","right":439.74609375,"bottom":61.74609375,"vCenter":405.24609375,"hCenter":55.74609375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"恰比编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":450,"top":12,"height":40.5,"width":9,"title":"E\nP\nQ","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":2,"name":3,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":3,"name":4,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":4,"name":5,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T02:00:48.532086(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 02:00:48.567[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 02:00:48.599[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:00:48.608[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:00:48.610[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:00:48.610[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:00:48.611[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 02:00:48.622[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 02:01:44.653[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 02:01:44.671[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 02:01:44.674[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 02:01:44.674[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 02:01:44.675[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"68","right":438,"bottom":39.75,"vCenter":403.5,"hCenter":20.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":25.5,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"Superb Gem Unc","right":439.74609375,"bottom":61.74609375,"vCenter":405.24609375,"hCenter":55.74609375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"恰比编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":450,"top":12,"height":40.5,"width":9,"title":"E\nP\nQ","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":4.5,"height":48,"width":67.5,"title":"二维码","right":532.5,"bottom":49.5,"vCenter":498.75,"hCenter":25.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":466.5,"top":52.5,"height":12,"width":66,"title":"ZK12000276","right":531.75,"bottom":64.5,"vCenter":498.75,"hCenter":58.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":2,"name":3,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":3,"name":4,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":4,"name":5,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T02:01:44.651995(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 02:01:44.705[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 02:01:44.734[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:01:44.743[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:01:44.746[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:01:44.746[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:01:44.746[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 02:01:44.757[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 02:01:46.326[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:01:46.340[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 02:01:46.343[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:01:46.343[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 02:01:46.344[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 02:01:46.355[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 02:01:46.384[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:46.394[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:46.399[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:46.399[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:46.399[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 02:01:46.405[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 02:01:55.004[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:55.016[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:55.019[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:55.020[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 02:01:55.021[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 02:01:55.028[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
