<template>
  <div class="paper-settings">
    <div class="paper-toolbar">
      <!-- 纸张大小选择 -->
      <div class="paper-size-group">
        <span class="toolbar-label">纸张大小:</span>
        <el-button-group>
          <el-button
            v-for="paper in paperSizes"
            :key="paper.type"
            :type="currentPaper.type === paper.type ? 'primary' : 'default'"
            size="small"
            @click="setPaperSize(paper.type, paper)"
          >
            {{ paper.name }}
          </el-button>
        </el-button-group>

        <!-- 自定义纸张按钮 -->
        <el-popover
          v-model:visible="showCustomPaper"
          placement="bottom"
          width="300"
          trigger="click"
        >
          <template #reference>
            <el-button
              :type="currentPaper.type === 'custom' ? 'primary' : 'default'"
              size="small"
            >
              自定义纸张
            </el-button>
          </template>

          <!-- 自定义纸张设置 -->
          <div class="custom-paper-form">
            <h4>自定义纸张尺寸</h4>
            <el-form :model="customPaper" label-width="60px" size="small">
              <el-form-item label="宽度">
                <el-input-number
                  v-model="customPaper.width"
                  :min="5"
                  :max="1000"
                  :step="1"
                  :precision="0"
                  style="width: 120px"
                />
                <span class="unit">mm</span>
              </el-form-item>
              <el-form-item label="高度">
                <el-input-number
                  v-model="customPaper.height"
                  :min="5"
                  :max="1000"
                  :step="1"
                  :precision="0"
                  style="width: 120px"
                />
                <span class="unit">mm</span>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="applyCustomPaper">
                  应用
                </el-button>
                <el-button size="small" @click="showCustomPaper = false">
                  取消
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-popover>
      </div>

      <!-- 纸张方向 -->
      <div class="paper-orientation-group">
        <span class="toolbar-label">方向:</span>
        <el-button-group>
          <el-button
            :type="orientation === 'portrait' ? 'primary' : 'default'"
            size="small"
            @click="setOrientation('portrait')"
          >
            纵向
          </el-button>
          <el-button
            :type="orientation === 'landscape' ? 'primary' : 'default'"
            size="small"
            @click="setOrientation('landscape')"
          >
            横向
          </el-button>
        </el-button-group>
      </div>

      <!-- 当前纸张信息显示 -->
      <div class="paper-info">
        <span class="info-text">
          当前: {{ currentPaper.name || currentPaper.type }}
          ({{ currentPaper.width }}×{{ currentPaper.height }}mm)
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
  hiprintTemplate: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['paper-change', 'orientation-change']);

// 标准纸张规格 (单位: mm)
const paperSizes = ref([
  { type: 'A3', name: 'A3', width: 297, height: 420 },
  { type: 'A4', name: 'A4', width: 210, height: 297 },
  { type: 'A5', name: 'A5', width: 148, height: 210 },
  { type: 'B3', name: 'B3', width: 353, height: 500 },
  { type: 'B4', name: 'B4', width: 250, height: 353 },
  { type: 'B5', name: 'B5', width: 176, height: 250 }
]);

// 当前纸张设置
const currentPaper = reactive({
  type: 'A4',
  name: 'A4',
  width: 210,
  height: 297
});

// 纸张方向
const orientation = ref('portrait'); // portrait | landscape

// 自定义纸张
const showCustomPaper = ref(false);
const customPaper = reactive({
  width: 210,
  height: 297
});

// 设置纸张大小
const setPaperSize = (type, paperInfo) => {
  try {
    let width = paperInfo.width;
    let height = paperInfo.height;

    // 根据方向调整尺寸
    if (orientation.value === 'landscape') {
      [width, height] = [height, width];
    }

    // 更新当前纸张信息
    Object.assign(currentPaper, {
      type,
      name: paperInfo.name,
      width,
      height
    });

    // 应用到hiprint模板
    if (props.hiprintTemplate && typeof props.hiprintTemplate.setPaper === 'function') {
      props.hiprintTemplate.setPaper(width, height);
      console.log(`设置纸张大小: ${paperInfo.name} (${width}×${height}mm)`);
    }

    // 触发事件
    emit('paper-change', { ...currentPaper });

    // EleMessage.success(`已切换到 ${paperInfo.name} 纸张`);
  } catch (error) {
    console.error('设置纸张大小失败:', error);
    EleMessage.error('设置纸张大小失败: ' + error.message);
  }
};

// 设置纸张方向
const setOrientation = (newOrientation) => {
  if (orientation.value === newOrientation) return;

  orientation.value = newOrientation;

  // 交换宽高
  const { width, height } = currentPaper;
  currentPaper.width = height;
  currentPaper.height = width;

  // 应用到hiprint模板
  if (props.hiprintTemplate && typeof props.hiprintTemplate.setPaper === 'function') {
    props.hiprintTemplate.setPaper(currentPaper.width, currentPaper.height);
  }

  emit('orientation-change', newOrientation);
  emit('paper-change', { ...currentPaper });

  // EleMessage.success(`已切换到${newOrientation === 'portrait' ? '纵向' : '横向'}模式`);
};

// 应用自定义纸张
const applyCustomPaper = () => {
  try {
    let width = customPaper.width;
    let height = customPaper.height;

    if (width < 5 || height < 5) {
      EleMessage.error('纸张尺寸不能小于50mm');
      return;
    }

    if (width > 1000 || height > 1000) {
      EleMessage.error('纸张尺寸不能大于1000mm');
      return;
    }

    // 根据方向调整尺寸
    if (orientation.value === 'landscape') {
      [width, height] = [height, width];
    }

    // 更新当前纸张信息
    Object.assign(currentPaper, {
      type: 'custom',
      name: '自定义',
      width,
      height
    });

    // 应用到hiprint模板
    if (props.hiprintTemplate && typeof props.hiprintTemplate.setPaper === 'function') {
      props.hiprintTemplate.setPaper(width, height);
      console.log(`设置自定义纸张大小: ${width}×${height}mm`);
    }

    showCustomPaper.value = false;
    emit('paper-change', { ...currentPaper });

    // EleMessage.success(`已应用自定义纸张 (${width}×${height}mm)`);
  } catch (error) {
    console.error('应用自定义纸张失败:', error);
    EleMessage.error('应用自定义纸张失败: ' + error.message);
  }
};

// 监听方向变化，同步更新自定义纸张的尺寸
watch(orientation, () => {
  if (currentPaper.type === 'custom') {
    customPaper.width = currentPaper.width;
    customPaper.height = currentPaper.height;
  }
});

// 暴露方法给父组件
defineExpose({
  setPaperSize,
  setOrientation,
  getCurrentPaper: () => ({ ...currentPaper }),
  getOrientation: () => orientation.value
});
</script>

<style scoped>
.paper-settings {
  margin-bottom: 16px;
}

.paper-toolbar {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.paper-size-group,
.paper-orientation-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.paper-info {
  margin-left: auto;
}

.info-text {
  font-size: 12px;
  color: #999;
  background: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.custom-paper-form {
  padding: 8px 0;
}

.custom-paper-form h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #333;
}

.unit {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .paper-toolbar {
    flex-wrap: wrap;
    gap: 16px;
  }

  .paper-info {
    margin-left: 0;
    width: 100%;
  }
}
</style>
